import React from "react";
import Image from "next/image";
import {
  SparklesIcon,
  Trophy,
  GraduationCap,
  Users,
  Award,
  Target,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { GlowingEffect } from "@/components/ui/glowing-effect";

// Grid item component for <PERSON>'s achievements
interface GridItemProps {
  area: string;
  icon: React.ReactNode;
  title: string;
  description: React.ReactNode;
}

const GridItem = ({ area, icon, title, description }: GridItemProps) => {
  return (
    <li className={`min-h-[14rem] list-none ${area}`}>
      <div className="relative h-full rounded-2xl border p-2 md:rounded-3xl md:p-3">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
        />
        <div className="border-0.75 relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6 md:p-6 dark:shadow-[0px_0px_27px_0px_#2D2D2D]">
          <div className="relative flex flex-1 flex-col justify-between gap-3">
            <div className="w-fit rounded-lg border border-gray-600 p-2">
              {icon}
            </div>
            <div className="space-y-3">
              <h3 className="-tracking-4 pt-0.5 font-sans text-xl/[1.375rem] font-semibold text-balance text-black md:text-2xl/[1.875rem] dark:text-white">
                {title}
              </h3>
              <h2 className="font-sans text-sm/[1.125rem] text-black md:text-base/[1.375rem] dark:text-neutral-400 [&_b]:md:font-semibold [&_strong]:md:font-semibold">
                {description}
              </h2>
            </div>
          </div>
        </div>
      </div>
    </li>
  );
};

// Ivan's achievements grid
function IvanAchievementsGrid() {
  return (
    <ul className="grid grid-cols-1 grid-rows-none gap-4 md:grid-cols-12 md:grid-rows-3 lg:gap-4 xl:max-h-[34rem] xl:grid-rows-2">
      <GridItem
        area="md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/5]"
        icon={<Trophy className="h-4 w-4 text-black dark:text-neutral-400" />}
        title="Мастер спорта України"
        description="Мастер спорта України по кікбоксингу, перший розряд по боксу. Багаторазовий чемпіон України."
      />

      <GridItem
        area="md:[grid-area:1/7/2/13] xl:[grid-area:2/1/3/5]"
        icon={
          <GraduationCap className="h-4 w-4 text-black dark:text-neutral-400" />
        }
        title="Вища освіта"
        description="Вища педагогічна освіта (ХНПУ), магістратура Харківської академії фізичної культури."
      />

      <GridItem
        area="md:[grid-area:2/1/3/7] xl:[grid-area:1/5/3/8]"
        icon={<Users className="h-4 w-4 text-black dark:text-neutral-400" />}
        title="17 років досвіду"
        description="Тренер по кікбоксингу та боксу. Вища тренерська категорія. Ідеальний тренер для новачків будь-якого віку."
      />

      <GridItem
        area="md:[grid-area:2/7/3/13] xl:[grid-area:1/8/2/13]"
        icon={<Award className="h-4 w-4 text-black dark:text-neutral-400" />}
        title="Чемпіон України"
        description="Багаторазовий чемпіон Чемпіонату України, багаторазовий чемпіон Кубків України, призер Чемпіонату СНД."
      />

      <GridItem
        area="md:[grid-area:3/1/4/13] xl:[grid-area:2/8/3/13]"
        icon={<Target className="h-4 w-4 text-black dark:text-neutral-400" />}
        title="Спеціалізація"
        description="Кікбоксинг, К-1 та бокс – початковий рівень, професіонали, СФП спеціальна фізична підготовка."
      />
    </ul>
  );
}

interface AboutIvanProps {
  title?: string;
  subtitle?: string;
}

export function AboutIvan({
  title = "Meet Ivan",
  subtitle = "Your Professional Boxing Coach",
}: AboutIvanProps) {
  return (
    <section id="about" className="w-full py-20 bg-white dark:bg-black">
      {/* Header Section */}
      <div className="text-center mb-16 px-6">
        <h2 className="text-4xl md:text-6xl font-bold text-neutral-800 dark:text-neutral-200 mb-4">
          {title}
        </h2>
        <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto">
          {subtitle} - Master of Sport in Kickboxing with 17 years of experience
          training fighters at all levels
        </p>
      </div>

      {/* Full Width Content - MouseTrailDemo Pattern */}
      <div className="grid md:grid-cols-12 gap-12 w-full">
        {/* Left side - Stats with MouseTrailDemo pattern */}
        <section className="h-4xl mx-auto my-12 w-full max-w-4xl rounded-[24px] border border-black/5 p-2 shadow-sm dark:border-white/5 md:rounded-t-[44px] md:col-span-8">
          <div className="relative mx-auto w-full rounded-[24px] border border-black/5 bg-neutral-800/5 shadow-sm dark:border-white/5 md:gap-8 md:rounded-b-[20px] md:rounded-t-[40px]">
            <article className="z-50 mt-20 flex flex-col items-center justify-center">
              <Badge
                variant="outline"
                className="mb-3 rounded-[14px] border border-black/10 bg-white text-base dark:border-white/5 dark:bg-neutral-800/5 md:left-6"
              >
                <SparklesIcon className="fill-[#EEBDE0] stroke-1 text-neutral-800" />{" "}
                Ivan&apos;s Achievements
              </Badge>
            </article>
            <section className="h-full p-6">
              <IvanAchievementsGrid />
            </section>
          </div>
        </section>

        {/* Right side - Photo */}
        <div className="flex items-center justify-center px-6 md:col-span-4">
          <div className="relative w-full max-w-md">
            <Image
              src="https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=500&h=600&fit=crop&crop=center"
              alt="Ivan - Professional Boxing Coach"
              width={500}
              height={600}
              className="rounded-2xl object-cover shadow-lg w-full h-auto"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default AboutIvan;
