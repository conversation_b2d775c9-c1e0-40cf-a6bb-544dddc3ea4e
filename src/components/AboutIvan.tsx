import React from "react";
import Image from "next/image";
import {
  SparklesIcon,
  Trophy,
  GraduationCap,
  Award,
  Target,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { GlowingEffect } from "@/components/ui/glowing-effect";

// Grid item component for <PERSON>'s achievements
interface GridItemProps {
  area: string;
  icon: React.ReactNode;
  title: string;
  description: React.ReactNode;
}

const GridItem = ({ area, icon, title, description }: GridItemProps) => {
  return (
    <li className={`min-h-[12rem] sm:min-h-[14rem] list-none ${area}`}>
      <div className="relative h-full rounded-2xl border p-2 md:rounded-3xl md:p-3">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
        />
        <div className="border-0.75 relative flex h-full flex-col justify-between gap-4 sm:gap-6 overflow-hidden rounded-xl p-4 sm:p-6 dark:shadow-[0px_0px_27px_0px_#2D2D2D]">
          <div className="relative flex flex-1 flex-col justify-between gap-2 sm:gap-3">
            <div className="w-fit rounded-lg border border-gray-600 p-2">
              {icon}
            </div>
            <div className="space-y-2 sm:space-y-3">
              <h3 className="-tracking-4 pt-0.5 font-sans text-lg sm:text-xl/[1.375rem] font-semibold text-balance text-black md:text-2xl/[1.875rem] dark:text-white">
                {title}
              </h3>
              <h2 className="font-sans text-sm/[1.125rem] text-black md:text-base/[1.375rem] dark:text-neutral-400 [&_b]:md:font-semibold [&_strong]:md:font-semibold">
                {description}
              </h2>
            </div>
          </div>
        </div>
      </div>
    </li>
  );
};

// Ivan's achievements grid with photo in center
function IvanAchievementsGrid() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 lg:gap-6">
      {/* Left side - 2 tiles */}
      <div className="md:col-span-2 space-y-4 lg:space-y-6">
        <GridItem
          area=""
          icon={<Trophy className="h-4 w-4 text-black dark:text-neutral-400" />}
          title="Майстер спорта України"
          description="Мастер спорта України по кікбоксингу, перший розряд по боксу. Багаторазовий чемпіон України."
        />
        <GridItem
          area=""
          icon={
            <GraduationCap className="h-4 w-4 text-black dark:text-neutral-400" />
          }
          title="Вища освіта"
          description="Вища педагогічна освіта (ХНПУ), магістратура Харківської академії фізичної культури."
        />
      </div>

      {/* Center - Ivan's Photo */}
      <div className="md:col-span-1 flex items-center justify-center">
        <div className="relative w-full max-w-[500px]">
          <Image
            src="/assets/ivan.webp"
            alt="Ivan - Professional Boxing Coach"
            width={500}
            height={600}
            className="rounded-2xl object-cover shadow-xl w-full h-auto"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-2xl"></div>

          {/* Photo Caption */}
          <div className="absolute bottom-4 left-4 right-4 text-center">
            <p className="text-white font-semibold text-sm bg-black/60 backdrop-blur-sm rounded-lg px-3 py-2">
              Іван - Майстер спорта
            </p>
          </div>
        </div>
      </div>

      {/* Right side - 2 tiles */}
      <div className="md:col-span-2 space-y-4 lg:space-y-6">
        <GridItem
          area=""
          icon={<Award className="h-4 w-4 text-black dark:text-neutral-400" />}
          title="Чемпіон України"
          description="Багаторазовий чемпіон Чемпіонату України, багаторазовий чемпіон Кубків України, призер Чемпіонату СНД."
        />
        <GridItem
          area=""
          icon={<Target className="h-4 w-4 text-black dark:text-neutral-400" />}
          title="Спеціалізація"
          description="Кікбоксинг, К-1 та бокс – початковий рівень, професіонали, СФП спеціальна фізична підготовка."
        />
      </div>
    </div>
  );
}

interface AboutIvanProps {
  title?: string;
  subtitle?: string;
}

export function AboutIvan({
  title = "Meet Ivan",
  subtitle = "Your Professional Boxing Coach",
}: AboutIvanProps) {
  return (
    <section
      id="about"
      className="w-full py-12 md:py-20 bg-white dark:bg-black"
    >
      {/* Header Section */}
      <div className="text-center mb-12 md:mb-16 px-4 md:px-6">
        <h2 className="text-3xl md:text-5xl lg:text-6xl font-bold text-neutral-800 dark:text-neutral-200 mb-4">
          {title}
        </h2>
        <p className="text-lg md:text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto">
          {subtitle} - Master of Sport in Kickboxing with 17 years of experience
          training fighters at all levels
        </p>
      </div>

      {/* Single Column Layout with Integrated Photo */}
      <div className="max-w-7xl mx-auto px-4 md:px-6">
        <section className="mx-auto w-full rounded-[24px] border border-black/5 p-2 shadow-sm dark:border-white/5 md:rounded-t-[44px]">
          <div className="relative mx-auto w-full rounded-[24px] border border-black/5 bg-neutral-800/5 shadow-sm dark:border-white/5 md:gap-8 md:rounded-b-[20px] md:rounded-t-[40px]">
            {/* Badge Header */}
            <article className="z-50 pt-12 md:pt-20 flex flex-col items-center justify-center">
              <Badge
                variant="outline"
                className="mb-6 md:mb-8 rounded-[14px] border border-black/10 bg-white text-sm md:text-base dark:border-white/5 dark:bg-neutral-800/5"
              >
                <SparklesIcon className="fill-[#EEBDE0] stroke-1 text-neutral-800 mr-2" />
                Ivan&apos;s Achievements
              </Badge>
            </article>

            {/* Achievements Grid with Photo in Center */}
            <section className="px-4 md:px-6 pb-6 md:pb-8">
              <IvanAchievementsGrid />
            </section>
          </div>
        </section>
      </div>
    </section>
  );
}

export default AboutIvan;
