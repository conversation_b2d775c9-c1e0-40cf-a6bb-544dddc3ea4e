import { Badge } from "@/components/ui/badge";
import { Instagram } from "lucide-react";

// Instagram embed component
interface InstagramEmbedProps {
  postUrl: string;
  caption?: string;
}

const InstagramEmbed = ({ postUrl, caption }: InstagramEmbedProps) => {
  // Extract post ID from URL for embed
  const getInstagramEmbedUrl = (url: string) => {
    // This is a simplified version - in production you'd want more robust URL parsing
    const postId = url.split("/p/")[1]?.split("/")[0];
    return `https://www.instagram.com/p/${postId}/embed/`;
  };

  return (
    <div className="relative bg-white dark:bg-neutral-900 rounded-2xl overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700">
      <div className="aspect-square">
        <iframe
          src={getInstagramEmbedUrl(postUrl)}
          className="w-full h-full"
          frameBorder="0"
          scrolling="no"
          allowTransparency={true}
          loading="lazy"
        />
      </div>
      {caption && (
        <div className="p-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">{caption}</p>
        </div>
      )}
    </div>
  );
};

// Real Instagram posts from Ivan's training
const instagramPosts = [
  {
    id: 1,
    postUrl: "https://www.instagram.com/p/Ck8S_6qDbFk/",
    caption: "Тренування з чемпіоном України 🥊",
  },
  {
    id: 2,
    postUrl: "https://www.instagram.com/p/DKMZEaqtIUw/",
    caption: "Підготовка до змагань 💪",
  },
  {
    id: 3,
    postUrl: "https://www.instagram.com/p/C2XI4avtVae/",
    caption: "Новачки показують прогрес 🔥",
  },
  {
    id: 4,
    postUrl: "https://www.instagram.com/p/C2kDdAfNWEH/",
    caption: "Техніка кікбоксингу в дії ⚡",
  },
  {
    id: 5,
    postUrl: "https://www.instagram.com/p/C9kM6ActEY8/",
    caption: "Спаринг з професіоналами 🥊",
  },
  {
    id: 6,
    postUrl: "https://www.instagram.com/p/CeD_urXDsBw/",
    caption: "Результати наших учнів 🏆",
  },
];

interface CommentsProps {
  title?: string;
  subtitle?: string;
}

export function Comments({
  title = "What Our Students Say",
  subtitle = "Follow our training journey on Instagram",
}: CommentsProps) {
  return (
    <section
      id="comments"
      className="w-full py-12 md:py-20 bg-gray-50 dark:bg-neutral-900"
    >
      {/* Header Section */}
      <div className="text-center mb-12 md:mb-16 px-4 md:px-6">
        <h2 className="text-3xl md:text-5xl lg:text-6xl font-bold text-neutral-800 dark:text-neutral-200 mb-4">
          {title}
        </h2>
        <p className="text-lg md:text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto mb-8">
          {subtitle}
        </p>

        {/* Instagram Badge */}
        <div className="flex justify-center">
          <Badge
            variant="outline"
            className="rounded-[14px] border border-black/10 bg-white text-sm md:text-base dark:border-white/5 dark:bg-neutral-800/5"
          >
            <Instagram className="w-4 h-4 mr-2 text-pink-500" />
            @ivan_boxing_coach
          </Badge>
        </div>
      </div>

      {/* Instagram Grid */}
      <div className="max-w-7xl mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {instagramPosts.map((post) => (
            <InstagramEmbed
              key={post.id}
              postUrl={post.postUrl}
              caption={post.caption}
            />
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <a
            href="https://instagram.com/ivan_boxing_coach"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold rounded-full hover:from-pink-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <Instagram className="w-5 h-5 mr-2" />
            Follow on Instagram
          </a>
        </div>
      </div>
    </section>
  );
}

export default Comments;
